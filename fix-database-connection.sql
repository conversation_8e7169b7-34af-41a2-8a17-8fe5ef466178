-- Fix Database Connection Issues
-- Run this script to ensure your database is properly set up

-- 1. Create database if it doesn't exist
-- Run this from psql as superuser first:
-- CREATE DATABASE requirement_service;

-- 2. Connect to the database
\c requirement_service;

-- 3. Drop existing tables to start fresh
DROP TABLE IF EXISTS ai_analyses CASCADE;
DROP TABLE IF EXISTS requirement_revisions CASCADE;
DROP TABLE IF EXISTS requirements CASCADE;
DROP TABLE IF EXISTS requirement_statuses CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- 4. Create tables with correct schema
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    company_id UUID NOT NULL,
    project_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE requirement_statuses (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    description TEXT
);

CREATE TABLE requirements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agentq_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    status_id INTEGER NOT NULL REFERENCES requirement_statuses(id),
    storage_url VARCHAR(500),
    uploaded_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE requirement_revisions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    requirement_id UUID NOT NULL REFERENCES requirements(id) ON DELETE CASCADE,
    revision_number INT NOT NULL,
    content_change TEXT,
    modified_by BIGINT REFERENCES users(id),
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(requirement_id, revision_number)
);

CREATE TABLE ai_analyses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    requirement_id UUID NOT NULL REFERENCES requirements(id) ON DELETE CASCADE,
    analysis_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ai_quality_score INT,
    ai_feedback JSONB
);

-- 5. Insert requirement statuses
INSERT INTO requirement_statuses (name, description) VALUES
('draft', 'The requirement has been uploaded but not yet processed.'),
('reviewed_by_ai', 'AI analysis is complete and awaiting user review.'),
('revised', 'The requirement has been modified by the user after AI review.'),
('approved', 'The user has approved the requirement.'),
('published', 'The requirement is finalized and ready for use.'),
('rejected', 'The user has rejected the requirement.');

-- 6. Create indexes
CREATE INDEX idx_requirements_status ON requirements(status_id);
CREATE INDEX idx_requirements_uploaded_by ON requirements(uploaded_by);
CREATE INDEX idx_requirements_created_at ON requirements(created_at);
CREATE INDEX idx_requirement_revisions_requirement_id ON requirement_revisions(requirement_id);
CREATE INDEX idx_ai_analyses_requirement_id ON ai_analyses(requirement_id);
CREATE INDEX idx_users_user_id ON users(user_id);
CREATE INDEX idx_users_project_id ON users(project_id);

-- 7. Verify tables were created
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- 8. Verify requirement statuses
SELECT * FROM requirement_statuses;
