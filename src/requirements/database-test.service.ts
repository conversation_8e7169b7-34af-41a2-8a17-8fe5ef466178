import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

@Injectable()
export class DatabaseTestService {
  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
  ) {}

  async testConnection(): Promise<any> {
    try {
      // Test basic connection
      const isConnected = this.dataSource.isInitialized;

      // Check if dataSource and options exist
      if (!this.dataSource) {
        return {
          error: 'DataSource not available',
          connected: false,
        };
      }

      if (!this.dataSource.options) {
        return {
          error: 'DataSource options not available',
          connected: false,
          dataSourceType: typeof this.dataSource,
          dataSourceKeys: Object.keys(this.dataSource),
        };
      }

      // Log the options type for debugging
      console.log('DataSource options type:', this.dataSource.options.type);
      console.log('DataSource options keys:', Object.keys(this.dataSource.options));

      // Test simple query
      const result = await this.dataSource.query('SELECT NOW() as current_time');

      // Check if tables exist
      const tables = await this.dataSource.query(`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
      `);

      // Cast to PostgresConnectionOptions to access host and port
      const pgOptions = this.dataSource.options as PostgresConnectionOptions;

      // More detailed debugging
      console.log('pgOptions after cast:', {
        type: pgOptions?.type,
        database: pgOptions?.database,
        host: pgOptions?.host,
        port: pgOptions?.port,
      });

      // Safely access properties with fallbacks
      return {
        connected: isConnected,
        currentTime: result[0]?.current_time,
        tables: tables.map((t: any) => t.table_name),
        databaseName: pgOptions?.database || 'unknown',
        host: pgOptions?.host || 'unknown',
        port: pgOptions?.port || 'unknown',
        optionsType: pgOptions?.type || 'unknown',
        debug: {
          hasOptions: !!this.dataSource.options,
          optionsKeys: Object.keys(this.dataSource.options),
        }
      };
    } catch (error) {
      console.error('Error in testConnection:', error);
      return {
        error: error.message,
        connected: false,
        stack: error.stack,
      };
    }
  }
}
