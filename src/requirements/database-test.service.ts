import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

@Injectable()
export class DatabaseTestService {
  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
  ) {}

  async testConnection(): Promise<any> {
    try {
      // Test basic connection
      const isConnected = this.dataSource.isInitialized;
      
      // Test simple query
      const result = await this.dataSource.query('SELECT NOW() as current_time');
      
      // Check if tables exist
      const tables = await this.dataSource.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);

      return {
        connected: isConnected,
        currentTime: result[0]?.current_time,
        tables: tables.map(t => t.table_name),
        databaseName: this.dataSource.options.database,
        host: this.dataSource.options.host,
        port: this.dataSource.options.port,
      };
    } catch (error) {
      return {
        error: error.message,
        connected: false,
      };
    }
  }
}
