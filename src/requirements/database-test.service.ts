import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

@Injectable()
export class DatabaseTestService {
  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
  ) {}

  async testConnection(): Promise<any> {
    try {
      // Test basic connection
      const isConnected = this.dataSource.isInitialized;
      
      // Test simple query
      const result = await this.dataSource.query('SELECT NOW() as current_time');
      
      // Check if tables exist
      const tables = await this.dataSource.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);

      // Cast to PostgresConnectionOptions to access host and port
      const pgOptions = this.dataSource.options as PostgresConnectionOptions;

      return {
        connected: isConnected,
        currentTime: result[0]?.current_time,
        tables: tables.map((t: any) => t.table_name),
        databaseName: pgOptions.database,
        host: pgOptions.host,
        port: pgOptions.port,
      };
    } catch (error) {
      return {
        error: error.message,
        connected: false,
      };
    }
  }
}
