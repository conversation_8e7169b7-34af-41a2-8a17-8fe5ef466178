import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Requirement, User, RequirementStatus } from '../entities';
import { UploadRequirementDto, QueryRequirementsDto } from './dto';
import { GoogleCloudStorageService } from '../storage/google-cloud-storage.service';

@Injectable()
export class RequirementsService {
  constructor(
    @InjectRepository(Requirement)
    private requirementRepository: Repository<Requirement>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(RequirementStatus)
    private requirementStatusRepository: Repository<RequirementStatus>,
    private googleCloudStorageService: GoogleCloudStorageService,
  ) {}

  async uploadRequirement(
    uploadDto: UploadRequirementDto,
    fileContent: string,
    fileBuffer: Buffer,
    originalFilename: string,
    mimetype: string,
  ): Promise<{ requirement_id: string; agentq_id: string; status: string; message: string }> {
    // Find or create user
    let user = await this.userRepository.findOne({
      where: { userId: uploadDto.user_id }
    });

    if (!user) {
      user = this.userRepository.create({
        userId: uploadDto.user_id,
        companyId: uploadDto.company_id,
        projectId: uploadDto.project_id,
      });
      await this.userRepository.save(user);
    }

    // Get draft status ID
    const draftStatus = await this.requirementStatusRepository.findOne({
      where: { name: 'draft' }
    });

    if (!draftStatus) {
      throw new Error('Draft status not found. Please ensure requirement statuses are seeded.');
    }

    // Generate AgentQ ID (simple counter-based for now)
    const count = await this.requirementRepository.count();
    const agentqId = `R-${String(count + 1).padStart(3, '0')}`;

    // Upload file to Google Cloud Storage
    const storageUrl = await this.googleCloudStorageService.uploadRequirementFile(
      fileBuffer,
      originalFilename,
      mimetype,
      agentqId,
    );

    // Create requirement
    const requirement = this.requirementRepository.create({
      agentqId,
      name: uploadDto.name,
      content: fileContent,
      statusId: draftStatus.id,
      storageUrl,
      uploadedById: user.id,
    });

    const savedRequirement = await this.requirementRepository.save(requirement);

    return {
      requirement_id: savedRequirement.id,
      agentq_id: savedRequirement.agentqId,
      status: 'draft',
      message: 'File uploaded successfully and is being processed asynchronously'
    };
  }

  async findAll(queryDto: QueryRequirementsDto): Promise<{
    data: any[];
    pagination: { page: number; limit: number; total: number; totalPages: number };
  }> {
    try {
      const {
        status,
        project_id,
        sort_by = 'created_at',
        order = 'DESC',
        page = 1,
        limit = 10
      } = queryDto;

    // Use find with relations instead of query builder to avoid join issues
    let relations = ['status', 'uploadedBy'];

    // Build where conditions
    if (status) {
      // We'll filter by status after loading
    }

    if (project_id) {
      // We'll filter by project_id after loading
    }

    // Get requirements with relations
    console.log('Attempting to find requirements with relations...');
    console.log('Relations:', relations);
    console.log('Sort by:', sort_by, 'Order:', order);

    // Map sort field names to entity property names
    const sortFieldMap: { [key: string]: string } = {
      'created_at': 'createdAt',
      'updated_at': 'updatedAt',
      'status': 'statusId'
    };

    const actualSortField = sortFieldMap[sort_by] || sort_by;
    console.log('Mapped sort field:', sort_by, '->', actualSortField);

    let requirements = await this.requirementRepository.find({
      relations,
      order: {
        [actualSortField]: order as 'ASC' | 'DESC'
      }
    });

    console.log('Found requirements before filtering:', requirements.length);

    // Apply filters after loading (since join conditions were problematic)
    if (status) {
      requirements = requirements.filter(req => req.status?.name === status);
    }

    if (project_id) {
      requirements = requirements.filter(req => req.uploadedBy?.projectId === project_id);
    }

    const total = requirements.length;

    // Apply pagination
    const offset = (page - 1) * limit;
    requirements = requirements.slice(offset, offset + limit);

    const data = requirements.map(req => ({
      id: req.id,
      agentq_id: req.agentqId,
      name: req.name,
      status: req.status?.name,
      storage_url: req.storageUrl,
      uploaded_by: req.uploadedBy?.userId,
      created_at: req.createdAt,
      updated_at: req.updatedAt,
    }));

      return {
        data,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error in findAll:', error);
      // Return empty result instead of throwing
      return {
        data: [],
        pagination: {
          page: queryDto.page || 1,
          limit: queryDto.limit || 10,
          total: 0,
          totalPages: 0,
        },
      };
    }
  }

  async findOne(id: string): Promise<any> {
    const requirement = await this.requirementRepository.findOne({
      where: { id },
      relations: ['status', 'uploadedBy'],
    });

    if (!requirement) {
      throw new NotFoundException(`Requirement with ID ${id} not found`);
    }

    return {
      id: requirement.id,
      agentq_id: requirement.agentqId,
      name: requirement.name,
      content: requirement.content,
      status: requirement.status?.name,
      storage_url: requirement.storageUrl,
      uploaded_by: requirement.uploadedBy?.userId,
      created_at: requirement.createdAt,
      updated_at: requirement.updatedAt,
    };
  }

  async debugDatabaseStatus(): Promise<any> {
    try {
      const requirementCount = await this.requirementRepository.count();
      const statusCount = await this.requirementStatusRepository.count();
      const userCount = await this.userRepository.count();

      const statuses = await this.requirementStatusRepository.find();
      const sampleRequirements = await this.requirementRepository.find({
        relations: ['status', 'uploadedBy'],
        order: { createdAt: 'DESC' },
        take: 1
      });
      const sampleRequirement = sampleRequirements.length > 0 ? sampleRequirements[0] : null;

      return {
        counts: {
          requirements: requirementCount,
          statuses: statusCount,
          users: userCount,
        },
        statuses: statuses,
        sampleRequirement: sampleRequirement,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  async debugRequirements(): Promise<any> {
    try {
      const count = await this.requirementRepository.count();
      const allRequirements = await this.requirementRepository.find();
      const requirementsWithRelations = await this.requirementRepository.find({
        relations: ['status', 'uploadedBy']
      });

      return {
        count,
        allRequirements: allRequirements.map(req => ({
          id: req.id,
          agentqId: req.agentqId,
          name: req.name,
          statusId: req.statusId,
          uploadedById: req.uploadedById,
        })),
        requirementsWithRelations: requirementsWithRelations.map(req => ({
          id: req.id,
          agentqId: req.agentqId,
          name: req.name,
          status: req.status?.name,
          uploadedBy: req.uploadedBy?.userId,
        })),
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
