import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue, Job } from 'bullmq';
import { v4 as uuidv4 } from 'uuid';
import { UploadJobData, UploadJobResult } from '../interfaces/upload-job.interface';
import { JobStatus, JobStatusResponseDto, JobStep } from '../dto/job-status-response.dto';

@Injectable()
export class UploadQueueService {
  private readonly logger = new Logger(UploadQueueService.name);

  constructor(
    @InjectQueue('upload-queue') private uploadQueue: Queue<UploadJobData, UploadJobResult>
  ) {}

  async addUploadJob(
    uploadDto: any,
    fileBuffer: Buffer,
    originalFilename: string,
    mimetype: string,
    fileContent: string
  ): Promise<string> {
    const jobId = `upload_${uuidv4()}`;
    
    const jobData: UploadJobData = {
      uploadDto,
      fileBuffer,
      originalFilename,
      mimetype,
      fileContent,
      jobId
    };

    const job = await this.uploadQueue.add('process-upload', jobData, {
      jobId,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 50, // Keep last 50 completed jobs
      removeOnFail: 20,     // Keep last 20 failed jobs
    });

    this.logger.log(`Upload job ${jobId} added to queue`);
    return jobId;
  }

  async getJobStatus(jobId: string): Promise<JobStatusResponseDto | null> {
    try {
      const job = await this.uploadQueue.getJob(jobId);
      
      if (!job) {
        return null;
      }

      const jobState = await job.getState();
      const progress = job.progress as any;
      
      let status: JobStatus;
      switch (jobState) {
        case 'waiting':
        case 'delayed':
          status = JobStatus.QUEUED;
          break;
        case 'active':
          status = JobStatus.PROCESSING;
          break;
        case 'completed':
          status = JobStatus.COMPLETED;
          break;
        case 'failed':
          status = JobStatus.FAILED;
          break;
        default:
          status = JobStatus.QUEUED;
      }

      const response: JobStatusResponseDto = {
        jobId,
        status,
        progress: {
          currentStep: progress?.step || JobStep.VALIDATING,
          percentage: progress?.percentage || 0,
          description: progress?.description || 'Job queued',
          updatedAt: progress?.updatedAt || job.timestamp
        },
        createdAt: new Date(job.timestamp),
        completedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
        error: job.failedReason || undefined,
        result: status === JobStatus.COMPLETED ? job.returnvalue : undefined
      };

      return response;
    } catch (error) {
      this.logger.error(`Error getting job status for ${jobId}:`, error);
      return null;
    }
  }

  async cancelJob(jobId: string): Promise<boolean> {
    try {
      const job = await this.uploadQueue.getJob(jobId);
      if (!job) {
        return false;
      }

      await job.remove();
      this.logger.log(`Job ${jobId} cancelled`);
      return true;
    } catch (error) {
      this.logger.error(`Error cancelling job ${jobId}:`, error);
      return false;
    }
  }

  async getQueueStats() {
    const waiting = await this.uploadQueue.getWaiting();
    const active = await this.uploadQueue.getActive();
    const completed = await this.uploadQueue.getCompleted();
    const failed = await this.uploadQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      total: waiting.length + active.length + completed.length + failed.length
    };
  }

  async retryFailedJob(jobId: string): Promise<boolean> {
    try {
      const job = await this.uploadQueue.getJob(jobId);
      if (!job) {
        return false;
      }

      await job.retry();
      this.logger.log(`Job ${jobId} retried`);
      return true;
    } catch (error) {
      this.logger.error(`Error retrying job ${jobId}:`, error);
      return false;
    }
  }
}
